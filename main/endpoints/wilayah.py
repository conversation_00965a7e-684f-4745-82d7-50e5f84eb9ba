from typing import Any, Dict

import httpx
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

router = APIRouter()


async def _proxy_wilayah(path: str) -> Dict[str, Any]:
    """
    Helper function to proxy requests to the Wilayah.id API.

    Args:
        path: The API path to fetch from wilayah.id

    Returns:
        JSON response from the upstream API

    Raises:
        HTTPException: If the upstream request fails or returns non-200 status
    """
    url = f"https://wilayah.id/api/{path}.json"

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=30.0)

        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Upstream API returned {response.status_code}",
            )

    except httpx.RequestError as e:
        raise HTTPException(
            status_code=502, detail=f"Failed to fetch data from upstream API: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(status_code=502, detail=f"Unexpected error: {str(e)}")


@router.get("/provinces")
async def get_provinces():
    """Get all provinces in Indonesia."""
    data = await _proxy_wilayah("provinces")
    return JSONResponse(content=data, headers={"Access-Control-Allow-Origin": "*"})


@router.get("/regencies/{province_code}")
async def get_regencies(province_code: str):
    """Get all regencies in a specific province."""
    data = await _proxy_wilayah(f"regencies/{province_code}")
    return JSONResponse(content=data, headers={"Access-Control-Allow-Origin": "*"})


@router.get("/districts/{regency_code}")
async def get_districts(regency_code: str):
    """Get all districts in a specific regency."""
    data = await _proxy_wilayah(f"districts/{regency_code}")
    return JSONResponse(content=data, headers={"Access-Control-Allow-Origin": "*"})


@router.get("/villages/{district_code}")
async def get_villages(district_code: str):
    """Get all villages in a specific district."""
    data = await _proxy_wilayah(f"villages/{district_code}")
    return JSONResponse(content=data, headers={"Access-Control-Allow-Origin": "*"})


@router.get("/{full_code:path}")
async def get_wilayah_data(full_code: str):
    """
    Catch-all route for any other Wilayah API paths.
    This allows for future API endpoints to be proxied automatically.
    """
    data = await _proxy_wilayah(full_code)
    return JSONResponse(content=data, headers={"Access-Control-Allow-Origin": "*"})
