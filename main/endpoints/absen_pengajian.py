from datetime import datetime, timedelta
from typing import List, Optional

from core.auth import verify_read_permission, verify_write_permission
from core.db import get_db_dependency
from fastapi import APIRouter, Depends, HTTPException
from schema.absen_pengajian_schema import (
    AbsenPengajian,
    AbsenPengajianCreate,
    AbsenPengajianRead,
)
from sqlmodel import Session, and_, select

router = APIRouter()


async def check_duplicate_pengajian(
    db: Session,
    data: AbsenPengajianCreate,
) -> bool:
    """Check for duplicate attendance records within a 2-hour window."""
    # Get the time window (2 hours before and after current time)
    time_window_start = data.tanggal - timedelta(hours=2)
    time_window_end = data.tanggal + timedelta(hours=2)

    # Query for duplicates within time window
    query = select(AbsenPengajian).where(
        AbsenPengajian.acara == data.acara,
        AbsenPengajian.nama == data.nama,
        AbsenPengajian.lokasi == data.lokasi,
        AbsenPengajian.ranah == data.ranah,
        AbsenPengajian.detail_ranah == data.detail_ranah,
        AbsenPengajian.tanggal >= time_window_start,
        AbsenPengajian.tanggal <= time_window_end,
    )

    result = db.exec(query).first()
    return result is not None


@router.post(
    "/",
    response_model=AbsenPengajianRead,
    dependencies=[Depends(verify_write_permission)],
)
async def create_absen(
    data: AbsenPengajianCreate,
    db: Session = Depends(get_db_dependency),
):
    """Create a new attendance record."""
    try:
        # Check for duplicates
        is_duplicate = await check_duplicate_pengajian(db=db, data=data)

        if is_duplicate:
            raise HTTPException(
                status_code=409,
                detail="Duplicate entry detected: Similar attendance record exists within 2 hours",
            )

        # Create AbsenPengajian instance
        db_absen = AbsenPengajian(**data.model_dump())

        db.add(db_absen)
        db.commit()
        db.refresh(db_absen)

        return AbsenPengajianRead.model_validate(db_absen)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get(
    "/",
    response_model=List[AbsenPengajianRead],
    dependencies=[Depends(verify_read_permission)],
)
async def list_absen(
    db: Session = Depends(get_db_dependency),
    tanggal: Optional[str] = None,
    acara: Optional[str] = None,
    lokasi: Optional[str] = None,
):
    """List attendance records with optional filters."""
    try:
        query = select(AbsenPengajian)

        # Apply filters if parameters are provided
        if tanggal:
            try:
                # Convert string date to datetime for comparison
                filter_date = datetime.strptime(tanggal, "%Y-%m-%d")
                # Compare only the date part
                query = query.filter(
                    and_(
                        AbsenPengajian.tanggal >= filter_date,
                        AbsenPengajian.tanggal < filter_date + timedelta(days=1),
                    )
                )
            except ValueError:
                raise HTTPException(
                    status_code=422,
                    detail="Invalid date format. Date should be YYYY-MM-DD",
                )

        if acara:
            query = query.filter(and_(AbsenPengajian.acara == acara))

        if lokasi:
            query = query.filter(and_(AbsenPengajian.lokasi == lokasi))

        absen_list = db.exec(query).all()
        return [AbsenPengajianRead.model_validate(absen) for absen in absen_list]

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get(
    "/{absen_id}",
    response_model=AbsenPengajianRead,
    dependencies=[Depends(verify_read_permission)],
)
async def get_absen(
    absen_id: int,
    db: Session = Depends(get_db_dependency),
):
    """Get a specific attendance record by ID."""
    try:
        absen = db.get(AbsenPengajian, absen_id)
        if absen is None:
            raise HTTPException(status_code=404, detail="Absen record not found")

        return AbsenPengajianRead.model_validate(absen)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")
