from datetime import datetime
from pathlib import Path
from typing import <PERSON>ple
from fastapi import HTTPException, UploadFile
from fastapi.responses import FileResponse
import string
import random


def generate_foto_id(length: int = 10) -> str:
    """Generate alpha-numeric string ID that doesn't start with 0"""
    chars = string.ascii_letters + string.digits
    first_char = random.choice(string.ascii_letters + "123456789")  # No 0 at start
    remaining_chars = "".join(random.choice(chars) for _ in range(length - 1))
    return first_char + remaining_chars


def generate_biodata_id(length: int = 6) -> str:
    """Generate 6-character alphanumeric ID for biodata generus"""
    chars = string.ascii_letters + string.digits
    # Ensure first character is not 0 for better readability
    first_char = random.choice(string.ascii_letters + "123456789")
    remaining_chars = "".join(random.choice(chars) for _ in range(length - 1))
    return first_char + remaining_chars


async def save_foto(
    foto: UploadFile, nama_id: str, tanggal: datetime
) -> Tuple[str, str]:
    """
    Save foto to disk and return foto_id and filename.

    Args:
        foto: The uploaded file
        nama_id: ID of the person
        tanggal: Date for the foto

    Returns:
        Tuple of (foto_id, filename)
    """
    # Create foto directory if it doesn't exist
    foto_dir = Path("fotos")
    foto_dir.mkdir(exist_ok=True)

    # Generate foto_id and filename
    foto_id = generate_foto_id()
    date_str = tanggal.strftime("%Y%m%d")

    # Get file extension, default to jpg
    file_extension = "jpg"
    if foto.filename and "." in foto.filename:
        file_extension = foto.filename.split(".")[-1].lower()

    filename = f"{nama_id}_{date_str}_{foto_id}.{file_extension}"

    # Save file to disk
    file_path = foto_dir / filename
    try:
        with open(file_path, "wb") as buffer:
            content = await foto.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save foto: {str(e)}")

    return foto_id, filename


def serve_foto(filename: str) -> FileResponse:
    """
    Serve foto file from disk by filename.

    Args:
        filename: Name of the foto file

    Returns:
        FileResponse with the image file
    """
    foto_dir = Path("fotos")
    file_path = foto_dir / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Foto not found")

    # Determine media type based on file extension
    extension = filename.split(".")[-1].lower()
    media_type_map = {
        "jpg": "image/jpeg",
        "jpeg": "image/jpeg",
        "png": "image/png",
        "gif": "image/gif",
        "webp": "image/webp",
    }
    media_type = media_type_map.get(extension, "image/jpeg")

    return FileResponse(path=file_path, media_type=media_type, filename=filename)


def validate_foto_size(foto_content: bytes, max_size_kb: int = 100) -> None:
    """
    Validate foto file size.

    Args:
        foto_content: The foto file content as bytes
        max_size_kb: Maximum allowed size in KB

    Raises:
        HTTPException: If foto exceeds size limit
    """
    if len(foto_content) > max_size_kb * 1024:
        raise HTTPException(
            status_code=422, detail=f"Foto size must be ≤{max_size_kb} kB"
        )
