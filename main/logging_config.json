{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"()": "uvicorn.logging.DefaultFormatter", "fmt": "%(levelprefix)s %(asctime)s [%(name)s] %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "access": {"()": "uvicorn.logging.AccessFormatter", "fmt": "%(levelprefix)s %(asctime)s %(client_addr)s - %(request_line)s %(status_code)s"}}, "handlers": {"default": {"formatter": "default", "class": "logging.StreamHandler", "stream": "ext://sys.stderr"}, "access": {"formatter": "access", "class": "logging.StreamHandler", "stream": "ext://sys.stdout"}}, "loggers": {"": {"handlers": ["default"], "level": "DEBUG"}, "uvicorn.error": {"level": "DEBUG", "handlers": ["default"], "propagate": false}, "uvicorn.access": {"handlers": ["access"], "level": "DEBUG", "propagate": false}}}