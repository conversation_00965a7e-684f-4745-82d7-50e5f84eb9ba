import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";
import legacy from "@vitejs/plugin-legacy";

import { cloudflare } from "@cloudflare/vite-plugin";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    legacy({
      targets: ["defaults", "not IE 11"],
    }),
    cloudflare(),
  ],
  css: {
    preprocessorOptions: {},
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  build: {
    //sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["vue", "vue-router", "pinia"],
        },
      },
    },
    modulePreload: {
      polyfill: true,
      resolveDependencies: (_filename, deps) => deps,
    },
  },
  optimizeDeps: {
    entries: ["./main.js"],
    include: ["vue", "vue-router", "pinia"],
  },
});
