import { createRouter, createWebHistory } from "vue-router";

// Lazy-loaded route components for better performance
const componentMap = {
  BiodataGenerus: () => import("./biodata-generus.vue"),
  PantauBiodataGenerus: () => import("./pantau-biodata-generus.vue"),
};

const routesConfig = [
  {
    path: "/biodata-generus",
    name: "BiodataGenerus",
    title: "Biodata Generus",
  },
  {
    path: "/pantau-biodata-generus",
    name: "PantauBiodataGenerus",
    title: "Pantauan Biodata Generus",
  },
];

const routes = routesConfig.map((route) => {
  // Destructure and extract needed properties, ignoring title since it's used via route.title
  const { name, path } = route;
  return {
    name,
    path,
    component: componentMap[name],
    meta: {
      title: route.title,
      requiresAuth: false,
    },
  };
});

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
