<template>
    <div id="app">
        <h1>Pantauan Biodata Generus</h1>

        <section>
            <div class="filter-item">
                <div>
                    <label for="desaFilter">Filter Desa:</label>
                    <select
                        v-model="filters.sambung_desa"
                        @change="filterTable"
                        id="desaFilter"
                    >
                        <option value="">Semua</option>
                        <option
                            v-for="desa in uniqueDesa"
                            :key="desa"
                            :value="desa"
                        >
                            {{ desa }}
                        </option>
                    </select>
                </div>
                <div>
                    <label for="kelompokFilter">Filter Kelompok:</label>
                    <select
                        v-model="filters.sambung_kelompok"
                        @change="filterTable"
                        id="kelompokFilter"
                    >
                        <option value="">Semua</option>
                        <option
                            v-for="kelompok in uniqueKelompok"
                            :key="kelompok"
                            :value="kelompok"
                        >
                            {{ kelompok }}
                        </option>
                    </select>
                </div>
                <div>
                    <label for="namaFilter">Filter Nama:</label>
                    <input
                        type="text"
                        v-model="filters.nama"
                        @input="filterTable"
                        placeholder="Cari nama..."
                        id="namaFilter"
                    />
                </div>
            </div>
        </section>

        <div class="table-container">
            <table id="biodataTable">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th @click="sortTable('nama_lengkap')">
                            Nama Lengkap
                            <span v-if="sortKey === 'nama_lengkap'">{{
                                sortOrder === "asc" ? "↑" : "↓"
                            }}</span>
                        </th>
                        <th @click="sortTable('nama_panggilan')">
                            Nama Panggilan
                            <span v-if="sortKey === 'nama_panggilan'">{{
                                sortOrder === "asc" ? "↑" : "↓"
                            }}</span>
                        </th>
                        <th @click="sortTable('sambung_desa')">
                            Desa
                            <span v-if="sortKey === 'sambung_desa'">{{
                                sortOrder === "asc" ? "↑" : "↓"
                            }}</span>
                        </th>
                        <th @click="sortTable('sambung_kelompok')">
                            Kelompok
                            <span v-if="sortKey === 'sambung_kelompok'">{{
                                sortOrder === "asc" ? "↑" : "↓"
                            }}</span>
                        </th>
                        <th @click="sortTable('jenis_kelamin')">
                            Jenis Kelamin
                            <span v-if="sortKey === 'jenis_kelamin'">{{
                                sortOrder === "asc" ? "↑" : "↓"
                            }}</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in filteredData" :key="index">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.nama_lengkap }}</td>
                        <td>{{ item.nama_panggilan }}</td>
                        <td>{{ item.sambung_desa }}</td>
                        <td>{{ item.sambung_kelompok }}</td>
                        <td>{{ item.jenis_kelamin }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="button-container">
            <button @click="downloadPDF">Download as PDF</button>
        </div>
    </div>
</template>

<script>
import { jsPDF } from "jspdf";
import "jspdf-autotable";

export default {
    data() {
        return {
            biodataData: [],
            filters: {
                sambung_desa: "",
                sambung_kelompok: "",
                nama: "",
            },
            apiKey: "",
            sortKey: "",
            sortOrder: "asc",
        };
    },
    computed: {
        uniqueDesa() {
            return [
                ...new Set(this.biodataData.map((item) => item.sambung_desa)),
            ].sort();
        },
        uniqueKelompok() {
            return [
                ...new Set(
                    this.biodataData.map((item) => item.sambung_kelompok),
                ),
            ].sort();
        },
        filteredData() {
            const filtered = this.biodataData.filter((item) => {
                const desaMatch =
                    !this.filters.sambung_desa ||
                    item.sambung_desa === this.filters.sambung_desa;
                const kelompokMatch =
                    !this.filters.sambung_kelompok ||
                    item.sambung_kelompok === this.filters.sambung_kelompok;
                const namaMatch =
                    !this.filters.nama ||
                    item.nama_lengkap
                        .toLowerCase()
                        .includes(this.filters.nama.toLowerCase()) ||
                    item.nama_panggilan
                        .toLowerCase()
                        .includes(this.filters.nama.toLowerCase());
                return desaMatch && kelompokMatch && namaMatch;
            });

            if (this.sortKey) {
                filtered.sort((a, b) => {
                    let aVal = this.sortKey === "index" ? 1 : a[this.sortKey];
                    let bVal = this.sortKey === "index" ? 1 : b[this.sortKey];

                    if (typeof aVal === "string") aVal = aVal.toLowerCase();
                    if (typeof bVal === "string") bVal = bVal.toLowerCase();

                    if (aVal < bVal) return this.sortOrder === "asc" ? -1 : 1;
                    if (aVal > bVal) return this.sortOrder === "asc" ? 1 : -1;
                    return 0;
                });
            }

            return filtered;
        },
    },
    methods: {
        filterTable() {
            // Empty method to handle @change event on filters
        },
        async fetchBiodataGenerus() {
            const apiUrl = "/api/biodata/generus/";
            try {
                const response = await fetch(apiUrl, {
                    headers: { Authorization: `ApiKey ${this.apiKey}` },
                });
                if (!response.ok)
                    throw new Error(`HTTP error! status: ${response.status}`);
                this.biodataData = await response.json();
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        },
        downloadPDF() {
            const doc = new jsPDF({
                unit: "cm",
                format: "a4",
                margins: { top: 1, bottom: 1, left: 1, right: 1 },
            });

            doc.setFont("times", "normal");
            doc.setFontSize(20);
            doc.text(
                "Laporan Biodata Generus",
                doc.internal.pageSize.getWidth() / 2,
                2,
                { align: "center" },
            );
            doc.setFontSize(16);
            doc.text(
                `${this.filters.sambung_desa || "Semua Desa"}`,
                doc.internal.pageSize.getWidth() / 2,
                2.8,
                { align: "center" },
            );

            doc.autoTable({
                head: [
                    [
                        "No.",
                        "Nama Lengkap",
                        "Nama Panggilan",
                        "Desa",
                        "Kelompok",
                        "Jenis Kelamin",
                    ],
                ],
                body: this.filteredData.map((item, index) => [
                    index + 1,
                    item.nama_lengkap,
                    item.nama_panggilan,
                    item.sambung_desa,
                    item.sambung_kelompok,
                    item.jenis_kelamin,
                ]),
                startY: 4,
                margin: { top: 1, right: 1, left: 1, bottom: 2 },
                styles: {
                    fontSize: 10,
                    cellPadding: 0.5,
                },
                pageBreak: "auto",
                bodyStyles: {
                    minCellHeight: 0.5,
                },
                didDrawPage: (data) => {
                    const pageNum = data.pageNumber;
                    const pageHeight = doc.internal.pageSize.height;
                    const pageWidth = doc.internal.pageSize.width;

                    doc.setFontSize(10);
                    const footerY = pageHeight - 1.5;

                    doc.setDrawColor(200, 200, 200);
                    doc.setLineWidth(0.02);
                    doc.line(1, footerY, pageWidth - 1, footerY);

                    const footerText = `BIODATA GENERUS - ${this.filters.sambung_desa || "Semua Desa"} - Halaman ${pageNum}`;
                    doc.text(footerText, pageWidth - 1, footerY + 0.5, {
                        align: "right",
                    });
                },
            });

            const filename = `Laporan-Biodata-Generus-${this.filters.sambung_desa || "semua"}.pdf`;
            doc.save(filename);
        },
        sortTable(key) {
            if (this.sortKey === key) {
                this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
            } else {
                this.sortKey = key;
                this.sortOrder = "asc";
            }
        },
    },
    mounted() {
        const params = new URLSearchParams(window.location.search);
        this.apiKey = params.get("key");
        this.fetchBiodataGenerus();
        document.title = "Pantauan Biodata Generus";
    },
};
</script>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

h1 {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 20px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background-color: #ffffff;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin-bottom: 20px;
    max-width: 550px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.filter-item div {
    display: flex;
    flex-direction: column;
}

.filter-item label {
    margin-bottom: 5px;
    font-weight: bold;
}

select,
input,
button {
    border: 1px solid #ccc;
    padding: 10px;
    font-size: 16px;
    background-color: #f9f9f9;
    appearance: none;
    border-radius: 20px;
    box-sizing: border-box;
    width: 100%;
    height: 45px;
}

select,
input {
    margin-top: 0;
    margin-bottom: 0;
}

button {
    width: auto;
    margin: 20px 10px;
    padding: 0 20px;
    display: inline-block;
    cursor: pointer;
    height: 50px;
}

.table-container {
    width: 100%;
    max-width: 768px;
    overflow-x: auto;
    border: 1px solid #ddd;
    border-radius: 10px;
    margin: 0 auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    min-width: 100%;
}

th,
td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    white-space: nowrap;
}

th {
    background-color: #f4f4f4;
    cursor: pointer;
    position: relative;
    user-select: none;
    transition: background-color 0.2s;
}

th:hover {
    background-color: #e0e0e0;
}

th span {
    margin-left: 5px;
    color: #666;
}

th:first-child,
th:nth-child(2) {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 20;
}

td:first-child,
td:nth-child(2) {
    position: sticky;
    left: 0;
    background-color: #fff;
    z-index: 10;
}

tr:last-child td {
    border-bottom: none;
}

th:first-child {
    cursor: default;
}

th:first-child:hover {
    background-color: #f4f4f4;
}

.button-container {
    text-align: center;
    margin-top: 20px;
    width: 100%;
    max-width: 550px;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 768px) {
    .filter-item {
        flex-direction: column;
        max-width: 100%;
    }

    button {
        width: 100%;
    }
}

section {
    width: 100%;
    display: flex;
    justify-content: center;
}
</style>
