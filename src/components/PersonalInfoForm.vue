<template>
    <div>
        <div class="section-title">Data Pribadi</div>
        <label for="nama_lengkap"><PERSON><PERSON></label>
        <input
            id="nama_lengkap"
            type="text"
            v-model="formData.nama_lengkap"
            placeholder="Nama <PERSON>"
            required
        />
        <label for="nama_panggilan">Nama <PERSON></label>
        <input
            id="nama_panggilan"
            type="text"
            v-model="formData.nama_panggilan"
            placeholder="Nama Panggilan"
            required
        />
        <div class="form-group">
            <label for="jenis_kelamin"><PERSON><PERSON></label>
            <select
                id="jenis_kelamin"
                v-model="formData.jenis_kelamin"
                required
            >
                <option value="" disabled selected>Pilih <PERSON><PERSON></option>
                <option value="LAKI-LAKI">LAKI-LAKI</option>
                <option value="PEREMPUAN">PEREMPUAN</option>
            </select>
        </div>
        <div class="form-group">
            <label for="kelahiran_tempat">Tempat Lahir</label>
            <input
                id="kelahiran_tempat"
                type="text"
                v-model="formData.kelahiran_tempat"
                placeholder="Tempat Lahir"
                required
            />
        </div>
        <div class="form-group">
            <label for="kelahiran_tanggal">Tanggal Lahir</label>
            <input
                id="kelahiran_tanggal"
                type="date"
                v-model="formData.kelahiran_tanggal"
                required
            />
        </div>
        <div class="form-group">
            <label for="alamat_tinggal">Alamat Tinggal (Lengkap)</label>
            <div class="alamat-tinggal-box">
                <AlamatTinggalForm
                    id="alamat_tinggal"
                    :value="formData.alamat_tinggal"
                    @input="updateAlamatTinggal"
                />
            </div>
        </div>
        <div class="form-group">
            <label for="nomor_hape">Nomor HP</label>
            <input
                id="nomor_hape"
                type="tel"
                v-model="formData.nomor_hape"
                placeholder="Nomor HP"
            />
        </div>
    </div>
</template>

<script>
import AlamatTinggalForm from "./AlamatTinggalForm.vue";

export default {
    props: {
        formData: {
            type: Object,
            required: true,
        },
    },
    components: {
        AlamatTinggalForm,
    },
    methods: {
        updateAlamatTinggal(value) {
            // Ensure we're dealing with a valid string value
            let stringValue = value;

            // More robustly detect and handle different types
            if (value === null || value === undefined) {
                console.log(
                    "PersonalInfoForm received null/undefined alamat_tinggal",
                );
                stringValue = "";
            }
            // Handle input event objects
            else if (
                value &&
                value.target &&
                value.target.value !== undefined
            ) {
                stringValue = value.target.value;
            }
            // Ignore InputEvent objects
            else if (
                value &&
                typeof value === "object" &&
                ((value.constructor &&
                    value.constructor.name === "InputEvent") ||
                    value instanceof Event)
            ) {
                console.log("PersonalInfoForm received InputEvent, ignoring");
                return;
            }
            // Handle anything else as a string
            else if (typeof value !== "string") {
                console.log(
                    "PersonalInfoForm received non-string value, converting:",
                    value,
                );
                try {
                    stringValue = String(value);
                } catch (e) {
                    console.error("Failed to convert value to string:", e);
                    return;
                }
            }

            console.log(
                "PersonalInfoForm updating alamat_tinggal to:",
                stringValue,
            );

            // Only update if value changed to avoid unnecessary refreshes
            if (this.formData.alamat_tinggal !== stringValue) {
                this.formData.alamat_tinggal = stringValue;
                // Force Vue to recognize the change
                this.$forceUpdate();
            }
        },
    },
    watch: {
        "formData.alamat_tinggal": (newVal) => {
            console.log(
                "PersonalInfoForm detected alamat_tinggal change:",
                newVal,
            );
        },
    },
    mounted() {
        console.log(
            "PersonalInfoForm mounted with alamat_tinggal:",
            this.formData.alamat_tinggal,
        );
        // Initialize with empty string if null/undefined
        if (
            this.formData.alamat_tinggal === null ||
            this.formData.alamat_tinggal === undefined
        ) {
            this.formData.alamat_tinggal = "";
            console.log("Initialized empty alamat_tinggal");
        }

        // Force the AlamatTinggalForm component to update with current value
        this.$nextTick(() => {
            // This ensures any child components get the updated value
            this.$forceUpdate();
        });
    },
};
</script>
<style scoped>
.alamat-tinggal-box {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 6px;
    margin-top: 8px;
    background-color: #f9f9f9;
}

#alamat_tinggal {
    width: 100%;
    margin: 0;
    padding: 0;
}
</style>
