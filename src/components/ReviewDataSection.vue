<template>
    <div class="review-data">
        <div class="section-title">Review Data</div>
        <div class="review-item">
            <strong><PERSON><PERSON>:</strong> {{ formData.nama_lengkap }}
        </div>
        <div class="review-item">
            <strong><PERSON><PERSON>:</strong> {{ formData.nama_panggilan }}
        </div>
        <div class="review-item">
            <strong><PERSON><PERSON>:</strong> {{ formData.jenis_kelamin }}
        </div>
        <div class="review-item">
            <strong>Tempat, Tanggal Lahir:</strong>
            {{ formData.kelahiran_tempat }},
            {{ formatDate(formData.kelahiran_tanggal) }}
        </div>
        <div class="review-item">
            <strong>Alamat Tinggal:</strong> {{ formData.alamat_tinggal || 'Tidak ada' }}
        </div>
        <div class="review-item">
            <strong>Nomor HP:</strong> {{ formData.nomor_hape || "-" }}
        </div>
        <div class="review-item">
            <strong>Desa/Kelompok:</strong> {{ formData.sambung_desa }} /
            {{ formData.sambung_kelompok }}
        </div>
        <div class="review-item">
            <strong>Sekolah & Kelas:</strong>
            {{ formData.sekolah_kelas || "-" }}
        </div>
        <div class="review-item" v-if="selectedHobi.length > 0">
            <strong>Hobi:</strong>
            <div
                v-for="(item, index) in selectedHobi"
                :key="index"
                class="review-hobi-item"
            >
                - {{ item.kategori }}: {{ item.hobi }}
            </div>
        </div>
        <div class="review-item">
            <strong>Nama Ayah:</strong> {{ formData.nama_ayah }}
        </div>
        <div class="review-item">
            <strong>Status Ayah:</strong> {{ formData.status_ayah }}
        </div>
        <div class="review-item">
            <strong>No. HP Ayah:</strong>
            {{ formData.nomor_hape_ayah || "-" }}
        </div>
        <div class="review-item">
            <strong>Nama Ibu:</strong> {{ formData.nama_ibu }}
        </div>
        <div class="review-item">
            <strong>Status Ibu:</strong> {{ formData.status_ibu }}
        </div>
        <div class="review-item">
            <strong>No. HP Ibu:</strong>
            {{ formData.nomor_hape_ibu || "-" }}
        </div>
        <div class="review-actions">
            <button @click="handleSubmit" :disabled="isSubmitting">
                {{ isSubmitting ? "Mengirim..." : "Kirim Data" }}
            </button>
            <button @click="handleEdit" class="secondary">Edit Data</button>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        formData: {
            type: Object,
            required: true,
        },
        selectedHobi: {
            type: Array,
            default: () => [],
        },
        isSubmitting: {
            type: Boolean,
            default: false,
        },
    },
    mounted() {
        console.log("ReviewDataSection mounted with alamat_tinggal:", this.formData.alamat_tinggal);
        // Ensure we capture the current address value
        this.savedAddress = this.formData.alamat_tinggal || '';
    },
    methods: {
        formatDate(dateString) {
            if (!dateString) return "";
            const options = { day: "numeric", month: "long", year: "numeric" };
            return new Date(dateString).toLocaleDateString("id-ID", options);
        },
        handleSubmit() {
            this.$emit("submit");
        },
        handleEdit() {
            // Log and save the alamat_tinggal value before emitting the edit event
            console.log("ReviewDataSection - alamat_tinggal before edit:", this.formData.alamat_tinggal);
            this.savedAddress = this.formData.alamat_tinggal || '';
            
            // Explicitly emit both the event and the saved address value
            this.$emit('edit', { savedAddress: this.savedAddress });
        }
    },
    data() {
        return {
            savedAddress: ''
        };
    },
};
</script>
