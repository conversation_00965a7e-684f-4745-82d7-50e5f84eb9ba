<template>
    <div class="photo-upload-container">
        <div class="section-title">Foto Profil</div>
        
        <!-- Photo Preview -->
        <div v-if="previewUrl" class="photo-preview">
            <img :src="previewUrl" alt="Preview foto" class="preview-image" />
            <button @click="removePhoto" class="remove-photo-btn" type="button">
                &times; Hapus Foto
            </button>
        </div>

        <!-- Upload Options -->
        <div v-if="!previewUrl" class="upload-options">
            <!-- Camera Capture -->
            <div class="camera-section">
                <button 
                    @click="openCamera" 
                    :disabled="isCapturing || !cameraSupported"
                    class="camera-btn"
                    type="button"
                >
                    📷 {{ isCapturing ? 'Membuka Kamera...' : 'Ambil Foto' }}
                </button>
                <p v-if="!cameraSupported" class="camera-warning">
                    Kamera tidak tersedia di perangkat ini
                </p>
            </div>

            <!-- File Upload -->
            <div class="file-section">
                <label for="photo-file" class="file-upload-label">
                    📁 Pilih File Foto
                </label>
                <input
                    id="photo-file"
                    type="file"
                    accept="image/*"
                    @change="handleFileSelect"
                    class="file-input"
                    ref="fileInput"
                />
            </div>
        </div>

        <!-- Camera Modal -->
        <div v-if="showCamera" class="camera-modal" @click="closeCamera">
            <div class="camera-content" @click.stop>
                <div class="camera-header">
                    <h3>Ambil Foto</h3>
                    <button @click="closeCamera" class="close-btn" type="button">&times;</button>
                </div>
                
                <div class="camera-viewport">
                    <video 
                        ref="videoElement" 
                        autoplay 
                        playsinline
                        class="camera-video"
                    ></video>
                    <canvas 
                        ref="canvasElement" 
                        class="camera-canvas"
                        style="display: none;"
                    ></canvas>
                </div>

                <div class="camera-controls">
                    <button @click="capturePhoto" class="capture-btn" type="button">
                        📸 Ambil Foto
                    </button>
                    <button @click="closeCamera" class="cancel-btn" type="button">
                        Batal
                    </button>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
        </div>
    </div>
</template>

<script>
export default {
    name: 'PhotoUpload',
    props: {
        modelValue: {
            type: File,
            default: null
        }
    },
    emits: ['update:modelValue'],
    data() {
        return {
            previewUrl: null,
            showCamera: false,
            isCapturing: false,
            cameraSupported: false,
            stream: null,
            errorMessage: null
        }
    },
    mounted() {
        this.checkCameraSupport()
    },
    beforeUnmount() {
        this.stopCamera()
    },
    methods: {
        checkCameraSupport() {
            this.cameraSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
        },

        async openCamera() {
            if (!this.cameraSupported) {
                this.errorMessage = 'Kamera tidak didukung di perangkat ini'
                return
            }

            this.isCapturing = true
            this.errorMessage = null

            try {
                this.stream = await navigator.mediaDevices.getUserMedia({
                    video: { 
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                })
                
                this.showCamera = true
                this.$nextTick(() => {
                    if (this.$refs.videoElement) {
                        this.$refs.videoElement.srcObject = this.stream
                    }
                })
            } catch (error) {
                console.error('Error accessing camera:', error)
                this.errorMessage = 'Tidak dapat mengakses kamera. Pastikan izin kamera telah diberikan.'
            } finally {
                this.isCapturing = false
            }
        },

        closeCamera() {
            this.stopCamera()
            this.showCamera = false
        },

        stopCamera() {
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop())
                this.stream = null
            }
        },

        capturePhoto() {
            const video = this.$refs.videoElement
            const canvas = this.$refs.canvasElement
            
            if (!video || !canvas) return

            const context = canvas.getContext('2d')
            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            
            context.drawImage(video, 0, 0, canvas.width, canvas.height)
            
            canvas.toBlob((blob) => {
                if (blob) {
                    const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' })
                    this.setPhoto(file)
                }
            }, 'image/jpeg', 0.8)

            this.closeCamera()
        },

        handleFileSelect(event) {
            const file = event.target.files[0]
            if (file) {
                this.setPhoto(file)
            }
        },

        setPhoto(file) {
            // Validate file size (500KB limit as per backend)
            const maxSize = 500 * 1024 // 500KB
            if (file.size > maxSize) {
                this.errorMessage = 'Ukuran file terlalu besar. Maksimal 500KB.'
                return
            }

            // Validate file type
            if (!file.type.startsWith('image/')) {
                this.errorMessage = 'File harus berupa gambar.'
                return
            }

            this.errorMessage = null
            
            // Create preview URL
            this.previewUrl = URL.createObjectURL(file)
            
            // Emit the file to parent component
            this.$emit('update:modelValue', file)
        },

        removePhoto() {
            if (this.previewUrl) {
                URL.revokeObjectURL(this.previewUrl)
                this.previewUrl = null
            }
            
            // Clear file input
            if (this.$refs.fileInput) {
                this.$refs.fileInput.value = ''
            }
            
            this.$emit('update:modelValue', null)
            this.errorMessage = null
        }
    }
}
</script>

<style scoped>
.photo-upload-container {
    margin: 20px 0;
}

.section-title {
    font-weight: bold;
    margin-bottom: 15px;
    color: #2c4a3e;
}

.photo-preview {
    position: relative;
    display: inline-block;
    margin-bottom: 15px;
}

.preview-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    border: 2px solid #ddd;
    object-fit: cover;
}

.remove-photo-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.camera-section, .file-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.camera-btn, .file-upload-label {
    background: #2c4a3e;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.camera-btn:hover, .file-upload-label:hover {
    background: #1e3329;
}

.camera-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.file-input {
    display: none;
}

.camera-warning {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

.camera-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.camera-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.camera-viewport {
    margin-bottom: 15px;
}

.camera-video {
    width: 100%;
    max-width: 640px;
    height: auto;
    border-radius: 8px;
}

.camera-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.capture-btn {
    background: #2c4a3e;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
}

.cancel-btn {
    background: #666;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
}

.error-message {
    color: #ff4444;
    font-size: 14px;
    margin-top: 10px;
    padding: 8px;
    background: #ffe6e6;
    border-radius: 4px;
    border: 1px solid #ffcccc;
}
</style>
