<template>
    <div class="form-group">
        <label for="sekolah_kelas"
            ><PERSON><PERSON> / Kerja / Wirausaha / Mu<PERSON>igh</label
        >
        <div class="suggestions-container">
            <input
                id="sekolah_kelas"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                :placeholder="placeholder"
            />
            <div
                class="suggestions"
                v-if="showSuggestions && filteredSekolahKelas.length"
            >
                <div
                    v-for="(item, index) in filteredSekolahKelas"
                    :key="index"
                    class="suggestion-item"
                    @click="selectSekolahKelas(item)"
                >
                    {{ item.jenjang }} {{ item.kelas }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SuggestionsMixin from "../mixins/SuggestionsMixin";

export default {
    mixins: [SuggestionsMixin],
    props: {
        formData: {
            type: Object,
            required: true,
        },
        sekolahKelasOptions: {
            type: Array,
            required: true,
        },
        placeholder: {
            type: String,
            default: "Ketik saja ...",
        },
    },
    computed: {
        filteredSekolahKelas() {
            const searchTerm = this.inputValue.toLowerCase();
            return searchTerm
                ? this.sekolahKelasOptions.filter(
                      ({ jenjang, kelas }) =>
                          jenjang.toLowerCase().includes(searchTerm) ||
                          kelas.toLowerCase().includes(searchTerm),
                  )
                : this.sekolahKelasOptions;
        },
    },
    methods: {
        handleInput() {
            this.showSuggestions = true;
            this.formData.sekolah_kelas = ""; // Clear selection on input
        },

        selectSekolahKelas({ jenjang, kelas }) {
            const formattedValue = `${jenjang} ${kelas}`;
            this.inputValue = formattedValue;
            this.formData.sekolah_kelas = formattedValue;
            this.showSuggestions = false;
        },
    },
};
</script>
