<template>
    <div class="form-group">
        <label for="hobi"><PERSON><PERSON> (pilih satu atau lebih)</label>
        <div class="suggestions-container">
            <div class="selected-hobi-container" v-if="selectedHobi.length > 0">
                <div
                    v-for="(item, index) in selectedHobi"
                    :key="index"
                    class="hobi-tag"
                >
                    {{ item.kategori }}: {{ item.hobi }}
                    <span class="remove-hobi" @click="removeHobi(index)"
                        >&times;</span
                    >
                </div>
            </div>
            <input
                id="hobi"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                placeholder="Ketik untuk mencari hobi"
            />
            <div
                class="suggestions"
                v-if="showSuggestions && filteredHobi.length"
            >
                <div
                    v-for="(item, index) in filteredHobi"
                    :key="index"
                    class="suggestion-item"
                    @click.stop="selectHobi(item)"
                >
                    <strong>{{ item.kategori }}</strong
                    >: {{ item.hobi }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SuggestionsMixin from "../mixins/SuggestionsMixin";

export default {
    mixins: [SuggestionsMixin],
    props: {
        hobiOptions: {
            type: Array,
            required: true,
        },
        selectedHobi: {
            type: Array,
            required: true,
        },
        placeholder: {
            type: String,
            default: "Ketik untuk mencari hobi",
        },
    },
    computed: {
        filteredHobi() {
            const searchTerm = this.inputValue.toLowerCase();
            if (!searchTerm) return [];

            return this.hobiOptions.filter(
                (item) =>
                    item.kategori.toLowerCase().includes(searchTerm) ||
                    item.hobi.toLowerCase().includes(searchTerm) ||
                    item.detail_hobi?.toLowerCase().includes(searchTerm),
            );
        },
    },
    methods: {
        selectHobi(item) {
            // Check if the item is already selected
            const isDuplicate = this.selectedHobi.some(
                (selectedItem) =>
                    selectedItem.kategori === item.kategori &&
                    selectedItem.hobi === item.hobi,
            );

            if (!isDuplicate) {
                this.selectedHobi.push({
                    kategori: item.kategori,
                    hobi: item.hobi,
                    detail_hobi: item.detail_hobi,
                });
            }

            // Clear input and hide suggestions
            this.inputValue = "";
            this.showSuggestions = false;
        },

        removeHobi(index) {
            this.selectedHobi.splice(index, 1);
        },
    },
};
</script>
