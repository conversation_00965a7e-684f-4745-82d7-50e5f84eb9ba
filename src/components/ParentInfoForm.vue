<template>
    <div>
        <div class="section-title">Data Orang Tua</div>
        <div class="form-group">
            <label for="nama_ayah">Nama Ayah</label>
            <input
                id="nama_ayah"
                type="text"
                v-model="formData.nama_ayah"
                placeholder="Nama Ayah"
                required
            />
        </div>
        <div class="form-group">
            <label for="status_ayah">Status Ayah</label>
            <select id="status_ayah" v-model="formData.status_ayah" required>
                <option value="" disabled selected>Pilih</option>
                <option value="SUDAH NGAJI">SUDAH NGAJI</option>
                <option value="BELUM NGAJI">BELUM NGAJI</option>
            </select>
        </div>
        <div class="form-group">
            <label for="nomor_hape_ayah">Nomor HP Ayah</label>
            <input
                id="nomor_hape_ayah"
                type="tel"
                v-model="formData.nomor_hape_ayah"
                placeholder="Nomor HP Ayah"
            />
        </div>
        <div class="form-group">
            <label for="nama_ibu">Nama Ibu</label>
            <input
                id="nama_ibu"
                type="text"
                v-model="formData.nama_ibu"
                placeholder="Nama Ibu"
                required
            />
        </div>
        <div class="form-group">
            <label for="status_ibu">Status Ibu</label>
            <select id="status_ibu" v-model="formData.status_ibu" required>
                <option value="" disabled selected>Pilih</option>
                <option value="SUDAH NGAJI">SUDAH NGAJI</option>
                <option value="BELUM NGAJI">BELUM NGAJI</option>
            </select>
        </div>
        <div class="form-group">
            <label for="nomor_hape_ibu">Nomor HP Ibu</label>
            <input
                id="nomor_hape_ibu"
                type="tel"
                v-model="formData.nomor_hape_ibu"
                placeholder="Nomor HP Ibu"
            />
        </div>
    </div>
</template>

<script>
export default {
    props: {
        formData: {
            type: Object,
            required: true,
        },
    },
};
</script>
