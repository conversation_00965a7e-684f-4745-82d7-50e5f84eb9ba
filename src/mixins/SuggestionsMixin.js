export default {
  props: {
    placeholder: {
      type: String,
      default: "Ketik untuk mencari...",
    },
  },

  data() {
    return {
      inputValue: "",
      showSuggestions: false,
      isComposing: false,
    };
  },

  methods: {
    handleInput() {
      this.showSuggestions = true;
      this.$emit("input-change", this.inputValue);
    },

    handleFocus() {
      if (this.inputValue) {
        this.showSuggestions = true;
      }
    },

    handleBlur() {
      // Delay hiding suggestions to allow click to register
      setTimeout(() => {
        this.showSuggestions = false;
      }, 150);
    },

    handleKeyup(_event) {
      const inputEl = this.$refs.inputEl;
      if (inputEl && this.inputValue !== inputEl.value) {
        this.inputValue = inputEl.value;
        this.handleInput();
      }
    },

    handleCompositionEnd(event) {
      this.isComposing = false;
      this.inputValue = event.target.value;
      this.handleInput();
    },

    handleCompositionStart() {
      this.isComposing = true;
    },
  },
};
